package com.ys;

import rk.netDevice.sdk.p2.*;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * 仁科网络设备数据接收Demo
 * 主要功能：接收并处理4个节点的传感器数据
 * 节点1：温湿度传感器
 * 节点2-4：倾角传感器X、Y、Z轴数据
 */
public class Demo {

    // API配置常量
    private static final String API_URL = "https://test.leanpec.com/testapi/api/runtime/script/action/e8c4aa98-48b5-4ce7-84c7-7d93fa1fb8c2";
    private static final String APPLICATION_HEADER = "fea05e48-03cc-4c4c-8fcf-99cfb23d18b1";
    private static final String AUTHORIZATION_HEADER = "63e2aa57-cfed-4d00-8a4a-241a89fa1dcd";

    /**
     * 向目标API发送传感器数据
     * @param temperature 温度值
     * @param humidity 湿度值
     * @param angleX X轴倾角
     * @param angleY Y轴倾角
     * @param angleZ Z轴倾角
     */
    private static void sendSensorDataToAPI(float temperature, float humidity,
                                            float angleX, float angleY, float angleZ) {
        // 使用异步方式发送数据，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                // 为每个传感器数据发送单独的API请求
                sendSingleDataToAPI("temperature", temperature);
                sendSingleDataToAPI("humidity", humidity);
                sendSingleDataToAPI("angleX", angleX);
                sendSingleDataToAPI("angleY", angleY);
                sendSingleDataToAPI("angleZ", angleZ);

                System.out.println("所有传感器数据发送完成");

            } catch (Exception e) {
                System.err.println("发送数据到API时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 向API发送单个数据点
     * @param dataType 数据类型（用于日志记录）
     * @param value 数值
     */
    private static void sendSingleDataToAPI(String dataType, float value) {
        try {
            // 格式化数值为两位小数
            float roundedValue = Math.round(value * 100.0f) / 100.0f;

            // 获取PLM字段值
            String chineseTypeName = getChineseTypeName(dataType);

            // 构造新格式的JSON数据
            String jsonData = buildNewFormatJson(roundedValue, chineseTypeName);

            // 发送HTTP POST请求
            boolean success = sendHttpPostRequest(jsonData);

            if (success) {
                System.out.println(chineseTypeName + " 数据发送成功: " + roundedValue);
            } else {
                System.out.println(chineseTypeName + " 数据发送失败: " + roundedValue);
            }

        } catch (Exception e) {
            System.err.println("发送" + dataType + "数据时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取数据类型对应的PLM值
     * @param dataType 英文数据类型
     * @return PLM字段值
     */
    private static String getChineseTypeName(String dataType) {
        switch (dataType) {
            case "temperature":
                return "机房温度";
            case "humidity":
                return "机房湿度";
            case "angleX":
                return "X轴角度";
            case "angleY":
                return "Y轴角度";
            case "angleZ":
                return "Z轴角度";
            default:
                return dataType;
        }
    }

    /**
     * 构造新格式的JSON数据
     * @param value 数值（已保留两位小数）
     * @param typeName PLM字段值（机房温度、机房湿度、X轴角度等）
     * @return JSON字符串
     */
    private static String buildNewFormatJson(float value, String typeName) {
        // 手动构造新格式的JSON
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"inputValues\": {");

        // 直接使用typeName作为PLM值
        json.append("\"plm\": \"").append(typeName).append("\",");

        json.append("\"value\": ").append(value);
        json.append("}");
        json.append("}");

        return json.toString();
    }



    /**
     * 发送HTTP POST请求
     * @param jsonData 要发送的JSON数据
     * @return 是否发送成功
     */
    private static boolean sendHttpPostRequest(String jsonData) {
        try {
            URL url = new URL(API_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法和头部
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Application", APPLICATION_HEADER);
            connection.setRequestProperty("Authorization", AUTHORIZATION_HEADER);
            connection.setDoOutput(true);

            // 发送JSON数据
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 检查响应状态码
            int responseCode = connection.getResponseCode();

            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("HTTP响应成功，状态码: " + responseCode);
                return true;
            } else {
                System.err.println("HTTP请求失败，状态码: " + responseCode);
                return false;
            }

        } catch (Exception e) {
            System.err.println("HTTP请求发生异常: " + e.getMessage());
            return false;
        }
    }

    public static void main(String[] args) throws IOException,
            InterruptedException {
        // 初始化RSServer，端口2404
        RSServer rsServer = RSServer.Initiate(65141);

        // 添加数据监听器
        rsServer.addDataListener(new IDataListener() {

            /**
             * 接收实时数据处理方法
             * @param data 实时数据对象
             */
            @Override
            public void receiveRealtimeData(RealTimeData data) {
                System.out.println("=== 接收到实时数据 ===");
                System.out.println("设备地址: " + data.getDeviceId());

                // 用于存储传感器数据
                float temperature = 0.0f;
                float humidity = 0.0f;
                float angleX = 0.0f;
                float angleY = 0.0f;
                float angleZ = 0.0f;

                // 遍历4个节点的数据
                for (NodeData nd : data.getNodeList()) {
                    int nodeId = nd.getNodeId();

                    switch (nodeId) {
                        case 1:
                            // 第一个节点：温湿度传感器
                            temperature = nd.getTem();
                            humidity = nd.getHum();
                            System.out.println("【节点1 - 温湿度传感器】");
                            System.out.println("温度: " + temperature + "°C");
                            System.out.println("湿度: " + humidity + "%");
                            break;

                        case 2:
                            // 第二个节点：倾角传感器X轴
                            angleX = Math.round((nd.getTem() / 10.0f) * 1000.0f) / 1000.0f;
                            System.out.println("【节点2 - 倾角传感器X轴】");
                            System.out.println("X轴倾角: " + angleX + "°");
                            break;

                        case 3:
                            // 第三个节点：倾角传感器Y轴
                            angleY = Math.round((nd.getTem() / 10.0f) * 1000.0f) / 1000.0f;
                            System.out.println("【节点3 - 倾角传感器Y轴】");
                            System.out.println("Y轴倾角: " + angleY + "°");
                            break;

                        case 4:
                            // 第四个节点：倾角传感器Z轴
                            angleZ = Math.round((nd.getTem() / 10.0f) * 1000.0f) / 1000.0f;
                            System.out.println("【节点4 - 倾角传感器Z轴】");
                            System.out.println("Z轴倾角: " + angleZ + "°");
                            break;

                        default:
                            // 未知节点
                            System.out.println("【未知节点" + nodeId + "】");
                            System.out.println("数据: " + nd.getTem());
                            break;
                    }
                }

                // 发送数据到目标API
                sendSensorDataToAPI(temperature, humidity, angleX, angleY, angleZ);

                System.out.println("===================\n");
            }

            /**
             * 接收登录数据处理方法
             * @param data 登录数据对象
             */
            @Override
            public void receiveLoginData(LoginData data) {
                System.out.println("设备登录 -> 设备地址: " + data.getDeviceId());
            }
            public void receiveStoreData(StoreData var1){
            };

            // 以下方法保留但不做具体处理，仅用于接口实现
            @Override
            public void receiveTimmingAck(TimmingAck data) {
            }

            @Override
            public void receiveTelecontrolAck(TelecontrolAck data) {
            }

            @Override
            public void receiveParamIds(ParamIdsData data) {
            }

            @Override
            public void receiveParam(ParamData data) {
            }

            @Override
            public void receiveWriteParamAck(WriteParamAck data) {
            }

            @Override
            public void receiveTransDataAck(TransDataAck data) {
            }

            @Override
            public void receiveHeartbeatData(HeartbeatData heartbeatData) {
            }
        });

        // 启动服务器
        rsServer.start();
        System.out.println("仁科网络设备数据接收服务已启动，端口: 65141");
        System.out.println("等待设备连接和数据传输...");
    }

}
